<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="slide_slide_view_form" model="ir.ui.view">
        <field name="name">slide.slide.view.form.inherit.survey</field>
        <field name="model">slide.slide</field>
        <field name="inherit_id" ref="website_slides.view_slide_slide_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='slide_category']" position="after">
                <field name="survey_id"
                    invisible="slide_category != 'certification'"
                    required="slide_category == 'certification'"
                    domain="[('certification', '=', True)]" context="{'default_certification': True, 'default_scoring_type': 'scoring_without_answers'}"/>
            </xpath>
            <xpath expr="//field[@name='is_preview']" position="attributes">
                <attribute name="invisible">slide_category == 'certification'</attribute>
            </xpath>
        </field>
    </record>

    <record id="slide_slide_action_certification" model="ir.actions.act_window">
        <field name="name">Certifications</field>
        <field name="res_model">slide.slide</field>
        <field name="view_mode">list,form,graph</field>
        <field name="domain">[('slide_category', '=', 'certification')]</field>
        <field name="context">{'default_slide_category': 'certification'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add a new certification
            </p>
        </field>
        <field name="view_id" ref="website_slides.view_slide_slide_tree"/>
    </record>
</odoo>
