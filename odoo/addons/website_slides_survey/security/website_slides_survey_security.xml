<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
    <!-- Specific survey access rules for slide channel officer-->
    <record id="survey_user_input_rule_slide_channel_officer" model="ir.rule">
        <field name="name">Survey user input: slide channel officer on certification: read</field>
        <field name="model_id" ref="survey.model_survey_user_input"/>
        <field name="domain_force">[('survey_id.certification', '=', True),
            ('survey_id.survey_type', 'in', ('survey', 'live_session', 'assessment', 'custom')),
            '|', ('survey_id.restrict_user_ids', '=', False), ('survey_id.restrict_user_ids', 'in', user.id)]</field>
        <field name="groups" eval="[(4, ref('website_slides.group_website_slides_officer'))]"/>
        <field name="perm_unlink" eval="0"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="0"/>
    </record>

    <record id="survey_user_input_line_rule_slide_channel_officer" model="ir.rule">
        <field name="name">Survey user input line: slide channel officer on certification: read</field>
        <field name="model_id" ref="survey.model_survey_user_input_line"/>
        <field name="domain_force">[('survey_id.certification', '=', True),
            ('survey_id.survey_type', 'in', ('survey', 'live_session', 'assessment', 'custom')),
            '|', ('survey_id.restrict_user_ids', '=', False), ('survey_id.restrict_user_ids', 'in', user.id)]</field>
        <field name="groups" eval="[(4, ref('website_slides.group_website_slides_officer'))]"/>
        <field name="perm_unlink" eval="0"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="0"/>
    </record>

    <record id="survey_question_answer_slide_channel_officer" model="ir.rule">
        <field name="name">Survey question answer: slide channel officer on certification: read</field>
        <field name="model_id" ref="survey.model_survey_question_answer"/>
        <field name="domain_force">[
            '|',
                '&amp;',
                    '&amp;', ('question_id.survey_id.certification', '=', True), ('question_id.survey_id.survey_type', 'in', ('survey', 'live_session', 'assessment', 'custom')),
                    '|', ('question_id.survey_id.restrict_user_ids', '=', False), ('question_id.survey_id.restrict_user_ids', 'in', user.id),
                '&amp;',
                    '&amp;', ('matrix_question_id.survey_id.certification', '=', True), ('matrix_question_id.survey_id.survey_type', 'in', ('survey', 'live_session', 'assessment', 'custom')),
                    '|', ('matrix_question_id.survey_id.restrict_user_ids', '=', False), ('matrix_question_id.survey_id.restrict_user_ids', 'in', user.id),
        ]</field>
        <field name="groups" eval="[(4, ref('website_slides.group_website_slides_officer'))]"/>
        <field name="perm_unlink" eval="0"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="0"/>
    </record>

    <record id="survey_question_rule_slide_channel_officer" model="ir.rule">
        <field name="name">Survey question: slide channel officer on certification: read</field>
        <field name="model_id" ref="survey.model_survey_question"/>
        <field name="domain_force">[('survey_id.certification', '=', True),
            ('survey_id.survey_type', 'in', ('survey', 'live_session', 'assessment', 'custom')),
            '|', ('survey_id.restrict_user_ids', '=', False),('survey_id.restrict_user_ids', 'in', user.id)]</field>
        <field name="groups" eval="[(4, ref('website_slides.group_website_slides_officer'))]"/>
        <field name="perm_unlink" eval="0"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="0"/>
    </record>

    <record id="survey_rule_slide_channel_officer" model="ir.rule">
        <field name="name">Survey: slide channel officer on certification: read</field>
        <field name="model_id" ref="survey.model_survey_survey"/>
        <field name="domain_force">[('certification', '=', True),
            ('survey_type', 'in', ('survey', 'live_session', 'assessment', 'custom')),
            '|', ('restrict_user_ids', '=', False),('restrict_user_ids', 'in', user.id)]</field>
        <field name="groups" eval="[(4, ref('website_slides.group_website_slides_officer'))]"/>
        <field name="perm_unlink" eval="0"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="0"/>
    </record>
    </data>
</odoo>
