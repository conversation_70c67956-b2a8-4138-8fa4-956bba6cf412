# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_wishlist
#
# Translators:
# <PERSON><PERSON><PERSON> <jaro.b<PERSON><PERSON>@ekoenergo.sk>, 2022
# <PERSON>, 2022
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:50+0000\n"
"PO-Revision-Date: 2022-09-22 05:57+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"Language: sk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.snippet_options
msgid ""
"<i class=\"fa fa-fw fa-heart\"/>\n"
"            Wishlist"
msgstr ""

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_add_to_wishlist
msgid "<i class=\"fa fa-heart-o me-2\" role=\"img\" aria-label=\"Add to wishlist\"/>Add to wishlist"
msgstr ""

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "<small><i class=\"fa fa-trash-o\"/> Remove</small>"
msgstr ""

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.add_to_wishlist
msgid "<span class=\"fa fa-heart\" role=\"img\" aria-label=\"Add to wishlist\"/>"
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__active
msgid "Active"
msgstr "Aktívne"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Add <span class=\"d-none d-md-inline\">to Cart</span>"
msgstr ""

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Add product to my cart but keep it in my wishlist"
msgstr ""

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.add_to_wishlist
msgid "Add to Wishlist"
msgstr ""

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_add_to_wishlist
msgid "Add to wishlist"
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Contact Us"
msgstr "Kontaktujte nás"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__currency_id
msgid "Default Currency"
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__display_name
msgid "Display Name"
msgstr "Zobrazovaný názov"

#. module: website_sale_wishlist
#: model:ir.model.constraint,message:website_sale_wishlist.constraint_product_wishlist_product_unique_partner_id
msgid "Duplicated wishlisted product for this partner."
msgstr "Duplikovaný produkt želaný pre tohto partnera."

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__id
msgid "ID"
msgstr "ID"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "My Wishlist"
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__partner_id
msgid "Owner"
msgstr "Majiteľ"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__price
msgid "Price"
msgstr "Cena"

#. module: website_sale_wishlist
#: model:ir.model.fields,help:website_sale_wishlist.field_product_wishlist__price
msgid "Price of the product when it has been added in the wishlist"
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__pricelist_id
msgid "Pricelist"
msgstr "Cenník"

#. module: website_sale_wishlist
#: model:ir.model.fields,help:website_sale_wishlist.field_product_wishlist__pricelist_id
msgid "Pricelist when added"
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_product_template
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__product_id
msgid "Product"
msgstr "Produkt"

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_product_product
msgid "Product Variant"
msgstr "Varianta produktu"

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_product_wishlist
msgid "Product Wishlist"
msgstr ""

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Product image"
msgstr "Obrázok produktu"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Shop Wishlist"
msgstr ""

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.snippet_options
msgid "Show Empty Wishlist"
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_res_users
msgid "User"
msgstr "Užívateľ"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__website_id
msgid "Website"
msgstr "Webstránka"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_res_partner__wishlist_ids
#: model:ir.model.fields,field_description:website_sale_wishlist.field_res_users__wishlist_ids
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.header_wishlist_link
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.snippet_options
msgid "Wishlist"
msgstr ""
