<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">
    <t t-inherit="mail.Activity" t-inherit-mode="extension">
        <xpath expr="//t[@name='tools']" position="replace">
            <t t-if="props.activity.request_partner_id and props.activity.res_model === 'slide.channel'">
                <button class="btn btn-link" t-on-click="onGrantAccess">
                    <i class="fa fa-check"/> Grant Access
                </button>
                <button class="btn btn-link" t-on-click="onRefuseAccess">
                    <i class="fa fa-times"/> Refuse Access
                </button>
            </t>
            <t t-else="">$0</t>
        </xpath>
    </t>
</templates>
