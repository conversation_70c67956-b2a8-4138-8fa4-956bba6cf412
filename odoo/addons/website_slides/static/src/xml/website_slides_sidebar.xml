<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <!-- JS Equivalent of the Python template "slide_sidebar_done_button" -->
    <t t-name="website.slides.sidebar.done.button">
        <div class="o_wslides_sidebar_done_button"
            t-att-data-id="slideId"
            t-att-data-uncompleted-icon="uncompletedIcon"
            t-att-data-completed="slideCompleted"
            t-att-data-can-self-mark-completed="canSelfMarkCompleted"
            t-att-data-can-self-mark-uncompleted="canSelfMarkUncompleted"
            t-att-data-is-member="isMember"> <!-- The template is only used for members -->
            <button class="o_wslides_button_complete btn btn-sm" t-if="slideCompleted and canSelfMarkUncompleted or !slideCompleted and canSelfMarkCompleted">
                <i t-if="slideCompleted" class="o_wslides_slide_completed fa fa-check-circle fa-fw text-success fa-lg" t-att-data-slide-id="slideId" title="Mark as not done"/>
                <i t-else="" t-attf-class="fa #{uncompletedIcon or 'fa-circle-thin'} fa-fw fa-lg" t-att-data-slide-id="slideId" title="Mark as done"/>
            </button>
            <button class="o_wslides_button_complete btn btn-sm border-0" t-else="" disabled="1">
                <i t-if="slideCompleted" class="o_wslides_slide_completed fa fa-check fa-fw text-success fa-lg" t-att-data-slide-id="slideId" title="Can not be marked as not done"/>
                <i t-else="" t-attf-class="fa #{uncompletedIcon or 'fa-circle-thin'} fa-fw fa-lg" t-att-data-slide-id="slideId" title="Can not be marked as done"/>
            </button>
        </div>
    </t>
</templates>
