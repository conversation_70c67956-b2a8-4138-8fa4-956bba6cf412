<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

  <t t-name="website_slides.SlideUnsubscribeDialog">
    <Dialog size="'md'" title="state.title">
      <div>
        <t t-if="state.mode === 'subscription'">
            <form class="clearfix">
                <div class="controls mt8">
                    <CheckBox value="isChecked" name="subscribed" onChange="(isChecked) => this.onChangeCheckbox(isChecked)">
                        Be notified when a new content is added.
                    </CheckBox>
                </div>
            </form>
        </t>
        <t t-if="state.mode === 'leave'">
            <p>Do you really want to leave the course?</p>
            <p>All progress will be lost until you rejoin this course.</p>
        </t>
      </div>

      <t t-set-slot="footer">
        <t t-if="state.mode === 'subscription'">
            <button class="btn btn-primary" t-att-disabled="state.buttonDisabled" t-on-click="() => this.onClickSubscriptionSubmit()">Save</button>
            <button class="btn" t-att-disabled="state.buttonDisabled" t-on-click="() => this.props.close()">Discard</button>
            <button class="btn btn-danger ms-auto" t-att-disabled="state.buttonDisabled" t-on-click="() => this.onClickLeaveCourse()">or Leave the course</button>
        </t>
        <t t-if="state.mode === 'leave'">
            <button class="btn btn-danger" t-att-disabled="state.buttonDisabled" t-on-click="() => this.onClickLeaveCourseSubmit()">Leave the course</button>
            <button class="btn" t-att-disabled="state.buttonDisabled" t-on-click="() => this.onClickLeaveCourseCancel()">Discard</button>
        </t>
      </t>
    </Dialog>
  </t>

</templates>
