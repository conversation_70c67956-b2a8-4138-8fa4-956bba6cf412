<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Get started: register to the platform -->
    <record id="badge_data_register" model="gamification.badge">
        <field name="name">Get started</field>
        <field name="description">Register to the platform</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/standard_badge_bronze.svg"/>
        <field name="is_published" eval="True"/>
        <field name="level">bronze</field>
        <field name="rule_auth">nobody</field>
    </record>
    <record id="badge_data_register_goal" model="gamification.goal.definition">
        <field name="name">Get started</field>
        <field name="description">Register to the platform</field>
        <field name="computation_mode">count</field>
        <field name="display_mode">boolean</field>
        <field name="model_id" ref="base.model_res_users"/>
        <field name="condition">higher</field>
        <field name="domain">[
            ('active', '!=', False),
            ('karma', '>', 0),
        ]</field>
        <field name="batch_mode">True</field>
        <field name="batch_distinctive_field" ref="base.field_res_users__id"/>
        <field name="batch_user_expression">user.id</field>
    </record>
    <record id="badge_data_register_challenge" model="gamification.challenge">
        <field name="name">Register to the platform</field>
        <field name="challenge_category">slides</field>
        <field name="period">once</field>
        <field name="visibility_mode">personal</field>
        <field name="report_message_frequency">never</field>
        <field name="reward_id" ref="badge_data_register"/>
        <field name="reward_realtime">True</field>
        <field name="user_domain">[('karma', '>', 0)]</field>
        <field name="state">inprogress</field>
    </record>
    <record id="badge_data_register_challenge_line_0" model="gamification.challenge.line">
        <field name="definition_id" ref="badge_data_register_goal"/>
        <field name="challenge_id" ref="badge_data_register_challenge"/>
        <field name="target_goal">1</field>
    </record>

    <!-- Know yourself: complete your profile -->
    <record id="badge_data_profile" model="gamification.badge">
        <field name="name">Know yourself</field>
        <field name="description">Complete your profile</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/standard_badge_bronze.svg"/>
        <field name="is_published" eval="True"/>
        <field name="level">bronze</field>
        <field name="rule_auth">nobody</field>
    </record>
    <record id="badge_data_profile_goal" model="gamification.goal.definition">
        <field name="name">Know yourself</field>
        <field name="description">Complete your profile</field>
        <field name="computation_mode">count</field>
        <field name="display_mode">boolean</field>
        <field name="model_id" ref="base.model_res_users"/>
        <field name="condition">higher</field>
        <field name="domain">[
            ('partner_id.country_id', '!=', False),
            ('partner_id.city', '!=', False),
            ('partner_id.email', '!=', False)
        ]</field>
        <field name="batch_mode">True</field>
        <field name="batch_distinctive_field" ref="base.field_res_users__id"/>
        <field name="batch_user_expression">user.id</field>
    </record>
    <record id="badge_data_profile_challenge" model="gamification.challenge">
        <field name="name">Complete your profile</field>
        <field name="challenge_category">slides</field>
        <field name="period">once</field>
        <field name="visibility_mode">personal</field>
        <field name="report_message_frequency">never</field>
        <field name="reward_id" ref="badge_data_profile"/>
        <field name="reward_realtime">True</field>
        <field name="user_domain">[('karma', '>', 0)]</field>
        <field name="state">inprogress</field>
    </record>
    <record id="badge_data_profile_challenge_line_0" model="gamification.challenge.line">
        <field name="definition_id" ref="badge_data_profile_goal"/>
        <field name="challenge_id" ref="badge_data_profile_challenge"/>
        <field name="target_goal">1</field>
    </record>

    <!-- Power User: complete a course -->
    <record id="badge_data_course" model="gamification.badge">
        <field name="name">Power User</field>
        <field name="description">Complete a course</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/standard_badge_silver.svg"/>
        <field name="is_published" eval="True"/>
        <field name="level">silver</field>
        <field name="rule_auth">nobody</field>
    </record>
    <record id="badge_data_course_goal" model="gamification.goal.definition">
        <field name="name">Power User</field>
        <field name="description">Complete a course</field>
        <field name="computation_mode">count</field>
        <field name="display_mode">boolean</field>
        <field name="model_id" ref="website_slides.model_slide_channel_partner"/>
        <field name="condition">higher</field>
        <field name="domain">[
            ('member_status', '=', 'completed')
        ]</field>
        <field name="batch_mode">True</field>
        <field name="batch_distinctive_field" ref="website_slides.field_slide_channel_partner__partner_id"/>
        <field name="batch_user_expression">user.partner_id.id</field>
    </record>
    <record id="badge_data_course_challenge" model="gamification.challenge">
        <field name="name">Complete a course</field>
        <field name="challenge_category">slides</field>
        <field name="period">once</field>
        <field name="visibility_mode">personal</field>
        <field name="report_message_frequency">never</field>
        <field name="reward_id" ref="badge_data_course"/>
        <field name="reward_realtime">True</field>
        <field name="user_domain">[('karma', '>', 0)]</field>
        <field name="state">inprogress</field>
    </record>
    <record id="badge_data_course_challenge_line_0" model="gamification.challenge.line">
        <field name="definition_id" ref="badge_data_course_goal"/>
        <field name="challenge_id" ref="badge_data_course_challenge"/>
        <field name="target_goal">1</field>
    </record>

    <!-- Certified Knowledge: get a certification -->
    <record id="badge_data_certification" model="gamification.badge">
        <field name="name">Certified Knowledge</field>
        <field name="description">Get a certification</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/standard_badge_gold.svg"/>
        <field name="is_published" eval="False"/>
        <field name="level">gold</field>
        <field name="rule_auth">nobody</field>
    </record>
    <record id="badge_data_certification_goal" model="gamification.goal.definition">
        <field name="name">Certified Knowledge</field>
        <field name="description">Get a certification</field>
        <field name="computation_mode">count</field>
        <field name="display_mode">boolean</field>
        <field name="model_id" ref="website_slides.model_slide_slide_partner"/>
        <field name="condition">higher</field>
        <field name="domain">[
            ('completed', '=', True),
            (0, '=', 1)
        ]</field>
        <field name="batch_mode">True</field>
        <field name="batch_distinctive_field" ref="website_slides.field_slide_slide_partner__partner_id"/>
        <field name="batch_user_expression">user.partner_id.id</field>
    </record>
    <record id="badge_data_certification_challenge" model="gamification.challenge">
        <field name="name">Get a certification</field>
        <field name="challenge_category">slides</field>
        <field name="period">once</field>
        <field name="visibility_mode">personal</field>
        <field name="report_message_frequency">never</field>
        <field name="reward_id" ref="badge_data_certification"/>
        <field name="reward_realtime">True</field>
        <field name="user_domain">[('karma', '>', 0)]</field>
        <field name="state">inprogress</field>
    </record>
    <record id="badge_data_certification_challenge_line_0" model="gamification.challenge.line">
        <field name="definition_id" ref="badge_data_certification_goal"/>
        <field name="challenge_id" ref="badge_data_certification_challenge"/>
        <field name="target_goal">1</field>
    </record>

    <!-- Community hero: reach 2000 XP -->
    <record id="badge_data_karma" model="gamification.badge">
        <field name="name">Community hero</field>
        <field name="description">Reach 2000 XP</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/standard_badge_gold.svg"/>
        <field name="is_published" eval="True"/>
        <field name="level">gold</field>
        <field name="rule_auth">nobody</field>
    </record>
    <record id="badge_data_karma_goal" model="gamification.goal.definition">
        <field name="name">Community hero</field>
        <field name="description">Reach 2000 XP</field>
        <field name="computation_mode">count</field>
        <field name="display_mode">boolean</field>
        <field name="model_id" ref="base.model_res_users"/>
        <field name="condition">higher</field>
        <field name="domain">[
            ('karma', '>=', 2000)
        ]</field>
        <field name="batch_mode">True</field>
        <field name="batch_distinctive_field" ref="base.field_res_users__id"/>
        <field name="batch_user_expression">user.id</field>
    </record>
    <record id="badge_data_karma_challenge" model="gamification.challenge">
        <field name="name">Reach 2000 XP</field>
        <field name="challenge_category">slides</field>
        <field name="period">once</field>
        <field name="visibility_mode">personal</field>
        <field name="report_message_frequency">never</field>
        <field name="reward_id" ref="badge_data_karma"/>
        <field name="reward_realtime">True</field>
        <field name="user_domain">[('karma', '>', 0)]</field>
        <field name="state">inprogress</field>
    </record>
    <record id="badge_data_karma_challenge_line_0" model="gamification.challenge.line">
        <field name="definition_id" ref="badge_data_karma_goal"/>
        <field name="challenge_id" ref="badge_data_karma_challenge"/>
        <field name="target_goal">1</field>
    </record>
</odoo>
