<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="slide_question_view_form" model="ir.ui.view">
        <field name="name">slide.question.view.form</field>
        <field name="model">slide.question</field>
        <field name="arch" type="xml">
            <form string="Quiz">
                <div invisible="answers_validation_error == ''">
                    <div class="alert alert-info" role="alert" aria-label="Validation error">
                        <i class="fa fa-info-circle" aria-hidden="true"/>
                        <field name="answers_validation_error" class="ms-2" readonly="1"/>
                    </div>
                </div>
                <sheet>
                    <div class="oe_title">
                        <label for="question" string="Question Name"/>
                        <h1><field options="{'line_breaks': False}" widget="text" name="question" default_focus="1" placeholder="e.g. What powers a computer?"/></h1>
                    </div>
                    <field name="answer_ids">
                        <list editable="bottom" create="true" delete="true">
                            <field name="display_name" column_invisible="True"/>
                            <field name="text_value"/>
                            <field name="is_correct"/>
                            <field name="comment"/>
                        </list>
                    </field>
                </sheet>
            </form>
        </field>
    </record>

    <record id="slide_question_view_tree" model="ir.ui.view">
        <field name="name">slide.question.view.list</field>
        <field name="model">slide.question</field>
        <field name="arch" type="xml">
            <list string="Quizzes">
                <field name="sequence" widget="handle"/>
                <field name="question"/>
                <field name="slide_id"/>
            </list>
        </field>
    </record>

    <record id="slide_question_view_tree_report" model="ir.ui.view">
        <field name="name">slide.question.view.list.report</field>
        <field name="model">slide.question</field>
        <field name="priority">20</field>
        <field name="arch" type="xml">
            <list string="Quizzes" create="0">
                <field name="sequence" widget="handle"/>
                <field name="question"/>
                <field name="slide_id"/>
                <field name="attempts_count"/>
                <field name="attempts_avg"/>
                <field name="done_count"/>
            </list>
        </field>
    </record>

    <record id="slide_question_view_search" model="ir.ui.view">
        <field name="name">slide.question.view.search</field>
        <field name="model">slide.question</field>
        <field name="arch" type="xml">
            <search string="Quizzes">
                <field name="question"/>
                <field name="slide_id"/>
            </search>
        </field>
    </record>

    <record id="slide_question_action_report" model="ir.actions.act_window">
        <field name="name">Quizzes</field>
        <field name="res_model">slide.question</field>
        <field name="view_mode">list,graph,pivot,form</field>
        <field name="view_id" ref="slide_question_view_tree_report"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Quiz data yet!
            </p>
            <p>
                Come back later to oversee how well your Attendees are doing.
            </p>
        </field>
    </record>
</odoo>
