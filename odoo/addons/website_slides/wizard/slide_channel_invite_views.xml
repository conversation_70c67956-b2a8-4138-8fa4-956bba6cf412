<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="slide_channel_invite_view_form" model="ir.ui.view">
            <field name="name">slide.channel.invite.view.form</field>
            <field name="model">slide.channel.invite</field>
            <field name="arch" type="xml">
                <form string="Compose Email" class="o_mail_composer_form">
                    <div class="alert alert-warning text-center" role="alert" invisible="channel_published or not channel_id">
                        This course is not published. Attendees may not be able to access its contents.
                    </div>
                    <sheet>
                        <field name="channel_id" invisible="1"/>
                        <field name="channel_published" invisible="1"/>
                        <field name="channel_visibility" invisible="1"/>
                        <field name="enroll_mode" invisible="1"/>
                        <group col="1">
                            <group col="2" invisible="enroll_mode or channel_visibility != 'public'">
                                <field name="channel_invite_url" readonly="1" widget="CopyClipboardChar"/>
                                <field name="send_email" widget="boolean_toggle" options="{'autosave': False}"/>
                            </group>
                            <group col="2" invisible="not send_email">
                                <field name="partner_ids"
                                    widget="many2many_tags_email"
                                    placeholder="Add contacts..."
                                    required="send_email"
                                    options="{'no_quick_create': True}"
                                    context="{'show_email': True, 'form_view_ref': 'base.view_partner_simple_form'}"/>
                            </group>
                            <group col="2" invisible="not send_email">
                                <field name="lang" invisible="1"/>
                                <field name="render_model" invisible="1"/>
                                <field name="subject" placeholder="Subject..."/>
                            </group>
                            <field name="can_edit_body" invisible="1"/>
                            <field name="body" class="oe-bordered-editor" widget="html_mail" invisible="not send_email" readonly="not can_edit_body" force_save="1"/>
                            <group invisible="not send_email">
                                <group>
                                    <field name="attachment_ids" widget="many2many_binary"/>
                                </group>
                                <group>
                                    <field name="template_id" label="Use template" context="{'default_model': 'slide.channel.partner'}"/>
                                </group>
                            </group>
                        </group>
                    </sheet>
                    <footer>
                        <button string="Send" invisible="not send_email" name="action_invite" type="object" class="btn-primary" data-hotkey="q"/>
                        <button string="Close" invisible="send_email" class="btn-secondary" special="cancel" data-hotkey="x"/>
                        <button string="Cancel" invisible="not send_email" class="btn-secondary" special="cancel" data-hotkey="x"/>
                    </footer>
                </form>
            </field>
        </record>
    </data>
</odoo>
