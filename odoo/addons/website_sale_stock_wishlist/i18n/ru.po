# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_stock_wishlist
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2024-01-30 15:14+0400\n"
"Last-Translator: \n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"<i class=\"fa fa-bell\"/>\n"
"                        We'll notify you once the product is back in stock."
msgstr ""
"<i class=\"fa fa-bell\"/>\n"
"                        Мы уведомим вас, как только товар снова появится на складе."

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"<i class=\"fa fa-times text-danger\"/>\n"
"                                Invalid email"
msgstr ""
"<i class=\"fa fa-times text-danger\"/>\n"
"                                Неверный адрес электронной почты"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"<small>\n"
"                            <i class=\"fa fa-envelope-o\"/>\n"
"                            Get notified when back in stock\n"
"                        </small>"
msgstr ""
"<small>\n"
"                           <i class=\"fa fa-envelope-o\"/>\n"
"                            Получайте уведомления о возвращении на склад\n"
"                        </small>"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"Add\n"
"                <span class=\"d-none d-md-inline\">to Cart</span>"
msgstr ""
"Добавить\n"
"               <span class=\"d-none d-md-inline\">в корзину</span>"

#. module: website_sale_stock_wishlist
#. odoo-javascript
#: code:addons/website_sale_stock_wishlist/static/src/xml/product_availability.xml:0
msgid "Add to wishlist"
msgstr "Добавить в избранное"

#. module: website_sale_stock_wishlist
#. odoo-javascript
#: code:addons/website_sale_stock_wishlist/static/src/xml/product_availability.xml:0
msgid "Added to your wishlist"
msgstr "Добавлено в ваш список желаний"

#. module: website_sale_stock_wishlist
#: model:ir.model,name:website_sale_stock_wishlist.model_product_template
msgid "Product"
msgstr "Товар"

#. module: website_sale_stock_wishlist
#: model:ir.model,name:website_sale_stock_wishlist.model_product_wishlist
msgid "Product Wishlist"
msgstr "Избранные товары"

#. module: website_sale_stock_wishlist
#. odoo-javascript
#: code:addons/website_sale_stock_wishlist/static/src/xml/product_availability.xml:0
msgid "Save for later"
msgstr "Сохранить"

#. module: website_sale_stock_wishlist
#: model:ir.model.fields,field_description:website_sale_stock_wishlist.field_product_wishlist__stock_notification
msgid "Stock Notification"
msgstr "Уведомление о запасах"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid "Temporarily out of stock"
msgstr "Временно нет в наличии"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid "<EMAIL>"
msgstr "<EMAIL>"
