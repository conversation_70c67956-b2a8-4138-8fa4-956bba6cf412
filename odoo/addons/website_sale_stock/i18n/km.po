# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_sale_stock
#
# Translators:
# <PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON>g <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2018-09-21 13:18+0000\n"
"Last-Translator: <PERSON>khann Seang <<EMAIL>>, 2018\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"Language: km\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "<span invisible=\"not show_availability\">Units</span>"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__stock_notification_partner_ids
msgid "Back in stock Notifications"
msgstr ""

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Continue Selling"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__allow_out_of_stock_order
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__allow_out_of_stock_order
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__allow_out_of_stock_order
msgid "Continue selling when out-of-stock"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "Dear Customer,"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid ""
"Default availability mode set on newly created storable products. This can "
"be changed at the product level."
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Default visibility for custom messages."
msgstr ""

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "Get notified when back in stock"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "How to display products having low quantities (on hand - reserved)"
msgstr ""

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/controllers/main.py:0
msgid "Invalid Email"
msgstr ""

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "Invalid email"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Inventory Defaults"
msgstr ""

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "Only"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "Order Now"
msgstr ""

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "Out of Stock"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Out-of-Stock"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__out_of_stock_message
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__out_of_stock_message
msgid "Out-of-Stock Message"
msgstr ""

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/product_product.py:0
#: model:ir.model,name:website_sale_stock.model_product_template
msgid "Product"
msgstr "ផលិតផល"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_product_product
msgid "Product Variant"
msgstr ""

#. module: website_sale_stock
#: model:ir.actions.server,name:website_sale_stock.ir_cron_send_availability_email_ir_actions_server
msgid "Product: send email regarding products availability"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "Regards,"
msgstr ""

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order
msgid "Sales Order"
msgstr ""

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Show Available Qty"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__available_threshold
msgid "Show Threshold"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__show_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__show_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__show_availability
msgid "Show availability Qty"
msgstr ""

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/sale_order.py:0
msgid ""
"Some products became unavailable and your cart has been updated. We're sorry "
"for the inconvenience."
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "The following product is now available."
msgstr ""

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/sale_order.py:0
msgid "The item has not been added to your cart since it is not available."
msgstr ""

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/product_product.py:0
msgid "The product '%(product_name)s' is now available"
msgstr ""

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/website_sale_reorder.js:0
msgid "This product is out of stock."
msgstr ""

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_stock_picking
msgid "Transfer"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
msgid "Units"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__website_warehouse_id
#: model:ir.model.fields,field_description:website_sale_stock.field_website__warehouse_id
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Warehouse"
msgstr ""

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "We'll notify you once the product is back in stock."
msgstr ""

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_website
#: model:ir.model.fields,field_description:website_sale_stock.field_stock_picking__website_id
msgid "Website"
msgstr "វែបសាយ"

#. module: website_sale_stock
#: model:ir.model.fields,help:website_sale_stock.field_stock_picking__website_id
msgid "Website where this order has been placed, for eCommerce orders."
msgstr ""

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "You already added"
msgstr ""

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "You already added all the available product in your cart."
msgstr ""

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/website_sale_reorder.js:0
msgid "You already have %s Units in your cart."
msgstr ""

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/sale_order.py:0
#: code:addons/website_sale_stock/models/sale_order_line.py:0
msgid "You ask for %(desired_qty)s products but only %(new_qty)s is available"
msgstr ""

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/website_sale_reorder.js:0
msgid "You ask for %s Units but only %s are available."
msgstr ""

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "in your cart."
msgstr ""

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "left in stock."
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "only if below"
msgstr ""

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "<EMAIL>"
msgstr ""
