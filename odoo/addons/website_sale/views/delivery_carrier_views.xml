<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_delivery_carrier_form_website_delivery" model="ir.ui.view">
        <field name="name">delivery.carrier.website.form</field>
        <field name="model">delivery.carrier</field>
        <field name="inherit_id" ref="delivery.view_delivery_carrier_form"/>
        <field name="arch" type="xml">
            <field name="company_id" position="after">
                <field
                    name="website_id"
                    placeholder="All websites"
                    groups="website.group_multi_website"
                    options="{'no_open': True, 'no_create_edit': True}"
                />
            </field>
            <field name="carrier_description" position="before">
                <field name="website_description"  placeholder="Description displayed on the eCommerce and on online quotations."/>
            </field>
            <button name="toggle_prod_environment" position="before">
                <button name="website_publish_button" type="object" class="oe_stat_button" icon="fa-globe">
                    <field name="is_published" widget="website_publish_button"/>
                </button>
            </button>
        </field>
    </record>

    <record id="view_delivery_carrier_tree" model="ir.ui.view">
        <field name="name">delivery.carrier.list.inherit</field>
        <field name="model">delivery.carrier</field>
        <field name="inherit_id" ref="delivery.view_delivery_carrier_tree"/>
        <field name="arch" type="xml">
            <field name="delivery_type" position="after">
                <field name="is_published"/>
                <field name="website_id" groups="website.group_multi_website"/>
            </field>
        </field>
    </record>

    <record id="view_delivery_carrier_search" model="ir.ui.view">
        <field name="name">delivery.carrier.search.inherit</field>
        <field name="model">delivery.carrier</field>
        <field name="inherit_id" ref="delivery.view_delivery_carrier_search"/>
        <field name="arch" type="xml">
            <filter name="inactive" position="after">
                <filter string="Published" name="is_published" domain="[('is_published', '=', True)]"/>
            </filter>
        </field>
    </record>

</odoo>
