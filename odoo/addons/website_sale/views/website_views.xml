<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="view_website_sale_website_form" model="ir.ui.view">
        <field name="name">website_sale.website.form</field>
        <field name="model">website</field>
        <field name="inherit_id" ref="website.view_website_form"/>
        <field name="arch" type="xml">
            <notebook position="inside">
                <page string="Product Page Extra Fields" name="page_product_page_extra_fields" groups="base.group_no_one">
                    <field name="shop_extra_field_ids" context="{'default_website_id': id}">
                        <list editable="bottom">
                            <field name="sequence" widget="handle"/>
                            <field name="field_id" required="1" options="{'no_create': True}"/>
                        </list>
                    </field>
                </page>
            </notebook>
        </field>
    </record>

</odoo>
