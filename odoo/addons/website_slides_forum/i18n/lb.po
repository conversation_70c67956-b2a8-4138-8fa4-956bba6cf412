# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_slides_forum
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:50+0000\n"
"PO-Revision-Date: 2019-08-26 09:16+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"Language: lb\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "(The above section was adapted from Stackoverflow’s FAQ.)"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "<b>Answers should not add or expand questions</b>. Instead either edit the question or add a question comment."
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "<b>Answers should not add or expand questions</b>. Instead, either edit the question or add a comment."
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "<b>Answers should not comment other answers</b>. Instead add a comment on the other answers."
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "<b>Answers should not start debates</b> This community Q&amp;A is not a discussion group. Please avoid holding debates in your answers as they tend to dilute the essence of questions and answers. For brief discussions please use commenting facility."
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "<b>Answers shouldn't just point to other Questions</b>. Instead add a question comment indication \"Possible duplicate of...\". However, it's ok to include links to other questions or answers providing relevant additional information."
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "<b>Answers shouldn't just point to other questions</b>.Instead add a comment indicating <i>\"Possible duplicate of...\"</i>. However, it's fine to include links to other questions or answers providing relevant additional information."
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "<b>Answers shouldn't just provide a link a solution</b>. Instead provide the solution description text in your answer, even if it's just a copy/paste. Links are welcome, but should be complementary to answer, referring sources or additional reading."
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "<b>Before you ask - please make sure to search for a similar question.</b> You can search questions by their title or tags. It’s also OK to answer your own question."
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "<b>Please avoid asking questions that are too subjective and argumentative</b> or not relevant to this community."
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "<b>What kinds of questions can I ask here?</b>"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "<b>What should I avoid in my answers?</b>"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "<b>What should I avoid in my questions?</b>"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "<b>Why can other people edit my questions/answers?</b>"
msgstr ""

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.slide_fullscreen
msgid "<i class=\"fa fa-comments\"/><span class=\"ms-1 d-none d-md-inline-block\">Forum</span>"
msgstr ""

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.website_slides_forum_header
msgid "<i class=\"fa fa-home\"/> Course"
msgstr ""

#. module: website_slides_forum
#: model:forum.forum,name:website_slides_forum.forum_forum_demo_channel_0
msgid "Basics of Gardening"
msgstr ""

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.website_slides_forum_index
msgid "Check our Courses <i class=\"oi oi-chevron-right\"/>"
msgstr ""

#. module: website_slides_forum
#: model_terms:ir.actions.act_window,help:website_slides_forum.forum_post_action_channel
msgid "Come back later to monitor and moderate what is posted on your Forums."
msgstr ""

#. module: website_slides_forum
#: model:ir.model,name:website_slides_forum.model_slide_channel
#: model:ir.model.fields,field_description:website_slides_forum.field_forum_forum__slide_channel_id
msgid "Course"
msgstr ""

#. module: website_slides_forum
#: model:ir.model.fields,field_description:website_slides_forum.field_slide_channel__forum_id
msgid "Course Forum"
msgstr ""

#. module: website_slides_forum
#: model:ir.model.fields,field_description:website_slides_forum.field_forum_forum__slide_channel_ids
msgid "Courses"
msgstr ""

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.website_slides_forum_index
msgid "Courses Discussions"
msgstr ""

#. module: website_slides_forum
#: model_terms:ir.actions.act_window,help:website_slides_forum.forum_forum_action_channel
msgid "Create a Forum"
msgstr ""

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.website_slides_forum_index
msgid "Description"
msgstr ""

#. module: website_slides_forum
#: model:ir.model.fields,help:website_slides_forum.field_forum_forum__slide_channel_ids
msgid "Edit the course linked to this forum on the course form."
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "For example, if you ask an interesting question or give a helpful answer, your input will be upvoted. On the other hand if the answer is misleading - it will be downvoted. Each vote in favor will generate 10 points, each vote against will subtract 2 points. There is a limit of 200 points that can be accumulated for a question or answer per day. The table given at the end explains reputation point requirements for each type of moderation task."
msgstr ""

#. module: website_slides_forum
#: model:ir.model,name:website_slides_forum.model_forum_forum
#: model:ir.ui.menu,name:website_slides_forum.website_slides_menu_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.course_main
#: model_terms:ir.ui.view,arch_db:website_slides_forum.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides_forum.website_slides_forum_channel_inherit_view_form
#: model_terms:ir.ui.view,arch_db:website_slides_forum.website_slides_forum_header
msgid "Forum"
msgstr ""

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.snippet_options
msgid "Forum Page"
msgstr ""

#. module: website_slides_forum
#: model:ir.actions.act_window,name:website_slides_forum.forum_post_action_channel
#: model_terms:ir.ui.view,arch_db:website_slides_forum.website_slides_forum_channel_inherit_view_form
msgid "Forum Posts"
msgstr ""

#. module: website_slides_forum
#: model:ir.model.fields,help:website_slides_forum.field_forum_forum__visibility
msgid "Forum linked to a Course, the visibility is the one applied on the course."
msgstr ""

#. module: website_slides_forum
#: model:ir.actions.act_window,name:website_slides_forum.forum_forum_action_channel
#: model:ir.ui.menu,name:website_slides_forum.website_slides_menu_forum_forum
msgid "Forums"
msgstr ""

#. module: website_slides_forum
#: model_terms:ir.actions.act_window,help:website_slides_forum.forum_forum_action_channel
msgid "Forums allow your attendees to ask questions to your community."
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "Here a table with the privileges and the karma level"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,welcome_message:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,welcome_message:website_slides_forum.forum_forum_demo_channel_2
msgid "Hide Intro"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "If this approach is not for you, please respect the community."
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "If you fit in one of these example or if your motivation for asking the question is “I would like to participate in a discussion about ______”, then you should not be asking here but on our mailing lists. However, if your motivation is “I would like others to explain ______ to me”, then you are probably OK."
msgstr ""

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.res_config_settings_view_form
msgid "Manage Forums"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "More over:"
msgstr ""

#. module: website_slides_forum
#: model_terms:ir.actions.act_window,help:website_slides_forum.forum_post_action_channel
msgid "No Forum Post yet!"
msgstr ""

#. module: website_slides_forum
#: model:ir.model.fields,field_description:website_slides_forum.field_slide_channel__forum_total_posts
msgid "Number of active forum posts"
msgstr ""

#. module: website_slides_forum
#: model:ir.model.constraint,message:website_slides_forum.constraint_slide_channel_forum_uniq
msgid "Only one course per forum!"
msgstr ""

#. module: website_slides_forum
#: model:ir.ui.menu,name:website_slides_forum.website_slides_menu_forum_post
msgid "Posts"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,welcome_message:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,welcome_message:website_slides_forum.forum_forum_demo_channel_2
msgid "Register"
msgstr ""

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.website_slides_forum_header
msgid "Reviews"
msgstr ""

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.website_slides_forum_index
msgid "See all Courses"
msgstr ""

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.snippet_options
msgid "Separate Courses"
msgstr ""

#. module: website_slides_forum
#: model:ir.model.fields,field_description:website_slides_forum.field_forum_forum__visibility
msgid "Show Course To"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "The goal of this site is create a relevant knowledge base that would answer questions related to Odoo."
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "Therefore questions and answers can be edited like wiki pages by experienced users of this site in order to improve the overall quality of the knowledge base content. Such privileges are granted based on user karma level: you will be able to do the same once your karma gets high enough."
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "This community is for professional and enthusiast users, partners and programmers. You can ask questions about:"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,welcome_message:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,welcome_message:website_slides_forum.forum_forum_demo_channel_2
msgid "This community is for professionals and enthusiasts of our products and services.<br>Share and discuss the best content and new marketing ideas, build your professional profile and become a better marketer together."
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "To prevent your question from being flagged and possibly removed, avoid asking subjective questions where …"
msgstr ""

#. module: website_slides_forum
#: model:forum.forum,name:website_slides_forum.forum_forum_demo_channel_2
msgid "Trees, Wood and Gardens"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,welcome_message:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,welcome_message:website_slides_forum.forum_forum_demo_channel_2
msgid "Welcome!"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "When a question or answer is upvoted, the user who posted them will gain some points, which are called \"karma points\". These points serve as a rough measure of the community trust to him/her. Various moderation tasks are gradually assigned to the users based on those points."
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "You should only ask practical, answerable questions based on actual problems that you face. Chatty, open-ended questions diminish the usefulness of this site and push other questions off the front page."
msgstr ""

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.forum_forum_view_form
msgid "eLearning"
msgstr ""

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.forum_post_view_graph_slides
msgid "eLearning Forum Posts"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "every answer is equally valid: “What’s your favorite ______?”"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "how to configure or customize Odoo to specific business needs,"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "how to develop modules for your own need,"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "how to install Odoo on a specific infrastructure,"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "it is a rant disguised as a question: “______ sucks, am I right?”"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "specific questions about Odoo service offers, etc."
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "there is no actual problem to be solved: “I’m curious if other people feel like I do.”"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "we are being asked an open-ended, hypothetical question: “What if ______ happened?”"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "what's the best way to use Odoo for a specific business need,"
msgstr ""

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "your answer is provided along with the question, and you expect more answers: “I use ______ for ______, what do you use?”"
msgstr ""
