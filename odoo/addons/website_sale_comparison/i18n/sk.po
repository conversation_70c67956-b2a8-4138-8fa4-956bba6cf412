# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_comparison
#
# Translators:
# <PERSON>, 2022
# <PERSON> <brenci<PERSON><PERSON><EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <jaro.<PERSON><PERSON><PERSON>@ekoenergo.sk>, 2022
# <PERSON>, 2022
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2022-09-22 05:57+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"Language: sk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n "
">= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_8
msgid "134.7 x 200 x 7.2 mm"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_7
msgid "308 g"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid ""
"<i class=\"fa fa-chevron-circle-down o_product_comparison_collpase\" "
"role=\"img\" aria-label=\"Collapse\" title=\"Collapse\"/>"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid ""
"<i class=\"fa fa-chevron-circle-down o_product_comparison_collpase\" "
"role=\"img\" aria-label=\"Collapse\" title=\"Collapse\"/><span>Tags</span>"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<i class=\"fa fa-shopping-cart me-2\"/>Add to Cart"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "<i class=\"fa fa-trash\" role=\"img\" aria-label=\"Remove\"/>"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_add_to_compare
msgid "<span class=\"fa fa-exchange me-2\"/>Compare"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<span>Tags</span>"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<strong>Price:</strong>"
msgstr "<strong>Cena:</strong>"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<strong>x</strong>"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_1
msgid "Apple"
msgstr ""

#. module: website_sale_comparison
#: model:ir.actions.act_window,name:website_sale_comparison.product_attribute_category_action
#: model:ir.ui.menu,name:website_sale_comparison.menu_attribute_category_action
msgid "Attribute Categories"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.snippet_options
msgid "Bottom of Page"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__name
msgid "Category Name"
msgstr "Meno kategórie"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.add_to_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_add_to_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.snippet_options
msgid "Compare"
msgstr ""

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/js/website_sale_comparison.js:0
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Compare Products"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Contact Us"
msgstr "Kontaktujte nás"

#. module: website_sale_comparison
#: model_terms:ir.actions.act_window,help:website_sale_comparison.product_attribute_category_action
msgid "Create a new attribute category"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_8
#: model:product.attribute.category,name:website_sale_comparison.product_attribute_category_2
msgid "Dimensions"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__display_name
msgid "Display Name"
msgstr "Zobrazovaný názov"

#. module: website_sale_comparison
#: model:product.attribute.category,name:website_sale_comparison.product_attribute_category_duration
msgid "Duration"
msgstr "Obdobie"

#. module: website_sale_comparison
#: model:product.attribute.category,name:website_sale_comparison.product_attribute_category_general_features
msgid "General Features"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.actions.act_window,help:website_sale_comparison.product_attribute_category_action
msgid ""
"Group attributes by category that will appear in the specification\n"
"                part of a product page."
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__id
msgid "ID"
msgstr "ID"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.snippet_options
msgid "None"
msgstr "Žiadne"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
msgid "Product"
msgstr "Produkt"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_attribute
msgid "Product Attribute"
msgstr "Atribút produktu"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_attribute_category
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attribute_category_tree_view
msgid "Product Attribute Category"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "Riadok atribútu na šablóne produktu"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_product
msgid "Product Variant"
msgstr "Varianta produktu"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "Product image"
msgstr "Obrázok produktu"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__attribute_ids
msgid "Related Attributes"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "Remove"
msgstr "Odstrániť"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__sequence
msgid "Sequence"
msgstr "Postupnosť"

#. module: website_sale_comparison
#: model:ir.model.fields,help:website_sale_comparison.field_product_attribute__category_id
msgid ""
"Set a category to regroup similar attributes under the same section in the "
"Comparison page of eCommerce."
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Shop Comparator"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.snippet_options
msgid "Specification"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
msgid "Specifications"
msgstr "Technické údaje"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Uncategorized"
msgstr "Nekategorizované"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
msgid "Warning"
msgstr "Varovanie"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_7
msgid "Weight"
msgstr "Hmotnosť"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
msgid "You can compare max 4 products."
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute__category_id
msgid "eCommerce Category"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
msgid "or"
msgstr "alebo"
