<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="1">

    <!-- CHANNEL 6: DIY Furniture -->
    <!-- ================================================== -->

    <record id="sale_order_course_1" model="sale.order">
        <field name="create_date" eval="DateTime.now() - relativedelta(days=31)"/>
        <field name="partner_id" ref="base.partner_demo"/>
        <field name="partner_invoice_id" ref="base.partner_demo"/>
        <field name="partner_shipping_id" ref="base.partner_demo"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="date_order" eval="DateTime.now() - relativedelta(days=30)"/>
        <field name="team_id" ref="sales_team.salesteam_website_sales"/>
        <field name="website_id" eval="1"/>
        <field name="tag_ids" eval="[(4, ref('sales_team.categ_oppor2')), (4, ref('sales_team.categ_oppor5'))]"/>
    </record>
    <record id="sale_order_course_1_line_1" model="sale.order.line">
        <field name="order_id" ref="sale_order_course_1"/>
        <field name="product_id" ref="website_sale_slides.product_course_channel_6"/>
        <field name="price_unit">100.0</field>
    </record>

    <record id="sale_order_course_2" model="sale.order">
        <field name="create_date" eval="DateTime.now() - relativedelta(days=100)"/>
        <field name="partner_id" ref="base.partner_demo_portal"/>
        <field name="partner_invoice_id" ref="base.partner_demo_portal"/>
        <field name="partner_shipping_id" ref="base.partner_demo_portal"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="date_order" eval="DateTime.now() - relativedelta(days=100)"/>
        <field name="team_id" ref="sales_team.salesteam_website_sales"/>
        <field name="website_id" eval="1"/>
    </record>
    <record id="sale_order_course_2_line_1" model="sale.order.line">
        <field name="order_id" ref="sale_order_course_2"/>
        <field name="product_id" ref="website_sale_slides.product_course_channel_1"/>
        <field name="price_unit">150.0</field>
    </record>
    <record id="sale_order_course_2_line_2" model="sale.order.line">
        <field name="order_id" ref="sale_order_course_2"/>
        <field name="product_id" ref="website_sale_slides.product_course_channel_6"/>
        <field name="price_unit">100.0</field>
    </record>

    <!-- CHANNEL 5: Basics of Furniture Creation -->
    <!-- ================================================== -->

    <record id="sale_order_course_3" model="sale.order">
        <field name="create_date" eval="DateTime.now() - relativedelta(days=31)"/>
        <field name="partner_id" ref="base.partner_demo"/>
        <field name="partner_invoice_id" ref="base.partner_demo"/>
        <field name="partner_shipping_id" ref="base.partner_demo"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="date_order" eval="DateTime.now() - relativedelta(days=30)"/>
        <field name="team_id" ref="sales_team.salesteam_website_sales"/>
        <field name="website_id" eval="1"/>
    </record>
    <record id="sale_order_course_3_line_1" model="sale.order.line">
        <field name="order_id" ref="sale_order_course_3"/>
        <field name="product_id" ref="website_sale_slides.product_course_channel_5"/>
        <field name="price_unit">200.0</field>
    </record>

    <record id="website_sale_slides_activity_1" model="mail.activity">
        <field name="res_id" ref="website_sale_slides.sale_order_course_1"/>
        <field name="res_model_id" ref="sale.model_sale_order"/>
        <field name="activity_type_id" ref="mail.mail_activity_data_call"/>
        <field name="date_deadline" eval="DateTime.today().strftime('%Y-%m-%d %H:%M')"/>
        <field name="summary">Suggest optional products</field>
        <field name="create_uid" ref="base.user_admin"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="website_sale_slides_activity_2" model="mail.activity">
        <field name="res_id" ref="website_sale_slides.sale_order_course_2"/>
        <field name="res_model_id" ref="sale.model_sale_order"/>
        <field name="activity_type_id" ref="mail.mail_activity_data_call"/>
        <field name="date_deadline" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M')"/>
        <field name="summary">Discuss discount</field>
        <field name="create_uid" ref="base.user_admin"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>

    <!-- Confirm sales -->
    <function model="sale.order" name="action_confirm" eval="[[
        ref('sale_order_course_1'),
        ref('sale_order_course_2'),
        ref('sale_order_course_3'),
    ]]"/>

</data></odoo>
