<?xml version="1.0" encoding="utf-8"?>
<odoo><data>
    <record id="slide_channel_view_form" model="ir.ui.view">
        <field name="name">slide.channel.view.form.inherit.sale</field>
        <field name="model">slide.channel</field>
        <field name="inherit_id" ref="website_slides.view_slide_channel_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='enroll']" position="after">
                <field name="product_id"
                    invisible="enroll != 'payment'"
                    required="enroll == 'payment'"
                    context="{
                        'default_type': 'service',
                        'default_service_tracking': 'course',
                        'default_invoice_policy': 'order',
                        'default_purchase_ok': False,
                        'default_sale_ok': True,
                        'default_website_published': True,
                    }"
                />
            </xpath>
            <xpath expr="//button[@name='action_redirect_to_members']" position="after">
                <button name="action_view_sales"
                    type="object"
                    icon="fa-usd"
                    class="oe_stat_button"
                    invisible="enroll != 'payment'"
                    groups="sales_team.group_sale_salesman">
                    <field name="product_sale_revenues" string="Sales" widget="statinfo"/>
                </button>
            </xpath>
        </field>
    </record>

    <record id="slide_channel_view_tree_report" model="ir.ui.view">
        <field name="name">slide.channel.view.list.report.inherit.sale_slides</field>
        <field name="model">slide.channel</field>
        <field name="inherit_id" ref="website_slides.slide_channel_view_tree_report"/>
        <field name="arch" type="xml">
            <field name="members_completed_count" position="after">
                <field name="currency_id" column_invisible="True"/>
                <field name="product_sale_revenues" string="Total Revenues" sum="Total Revenues" widget="monetary"/>
            </field>
        </field>
    </record>

    <record id="slide_channel_view_kanban" model="ir.ui.view">
        <field name="name">slide.channel.view.kanban.inherit.sale</field>
        <field name="model">slide.channel</field>
        <field name="inherit_id" ref="website_slides.slide_channel_view_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='info_avg_rating']" position="after">
                <div class="d-flex" invisible="enroll != 'payment'">
                    <label for="product_sale_revenues" class="mb0 me-auto">Sales</label>
                    <field class="text-nowrap" name="product_sale_revenues" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                    <field name="currency_id" invisible="True"/>
                </div>
            </xpath>
        </field>
    </record>

    <record id="slide_channel_view_form_add_inherit_sale_slides" model="ir.ui.view">
        <field name="name">slide.channel.view.form.add.inherit.sale.slides</field>
        <field name="model">slide.channel</field>
        <field name="inherit_id" ref="website_slides.slide_channel_view_form_add"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='allow_comment']" position="after">
                <field name="enroll" widget="radio" options="{'horizontal': true}" string="Enroll Policy"/>
                <field name="product_id" invisible="enroll != 'payment'" required="enroll == 'payment'"
                    context="{
                        'default_type': 'service',
                        'default_service_tracking': 'course',
                        'default_invoice_policy': 'order',
                        'default_purchase_ok': False,
                        'default_sale_ok': True,
                        'default_website_published': True,
                    }"
                />
    		</xpath>
        </field>
    </record>
</data></odoo>
